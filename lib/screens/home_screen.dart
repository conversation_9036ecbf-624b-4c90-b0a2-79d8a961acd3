import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:money_mouthy_two/screens/create_post.dart';
import 'package:money_mouthy_two/services/wallet_service.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/follow_service.dart';
import 'package:money_mouthy_two/widgets/profile_drawer.dart';
import 'package:money_mouthy_two/screens/profile_screen.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';
import 'package:money_mouthy_two/widgets/post_card.dart';
import 'package:money_mouthy_two/widgets/skeleton_loader.dart';

// Category data model
class CategoryData {
  final String name;
  final Color color;
  final double topPrice;

  const CategoryData({
    required this.name,
    required this.color,
    required this.topPrice,
  });
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 0;

  static const List<CategoryData> categories = [
    CategoryData(name: 'News', color: Color(0xFF29CC76), topPrice: 0.0),
    CategoryData(name: 'Politics', color: Color(0xFF4C5DFF), topPrice: 0.0),
    CategoryData(name: 'Sex', color: Color(0xFFFF4081), topPrice: 0.0),
    CategoryData(
      name: 'Entertainment',
      color: Color(0xFFA06A00),
      topPrice: 0.0,
    ),
    CategoryData(name: 'Sports', color: Color(0xFFC43DFF), topPrice: 0.0),
    CategoryData(name: 'Religion', color: Color(0xFF000000), topPrice: 0.0),
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await WalletService().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  void _onCategorySelected(int index) {
    setState(() {
      currentCategoryIndex = index;
    });
  }

  Future<void> _navigateToCreatePost() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreatePostScreen(),
        settings: RouteSettings(
          arguments: {
            'selectedCategory': categories[currentCategoryIndex].name,
          },
        ),
      ),
    );

    // If post was successfully created, set category to Politics
    if (result != null && result is Map && result['success'] == true) {
      setState(() {
        // Find Politics category index (should be index 1)
        final politicsIndex = categories.indexWhere(
          (cat) => cat.name == 'Politics',
        );
        if (politicsIndex != -1) {
          currentCategoryIndex = politicsIndex;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth >= 768;

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          drawer: !isWideScreen ? ProfileDrawer() : null,
          appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
          body: isWideScreen ? _buildWideScreenLayout() : _buildMobileLayout(),
          bottomNavigationBar: HomeBottomNavigationBar(
            currentCategoryIndex: currentCategoryIndex,
            categories: categories,
            onNavigateToCreatePost: _navigateToCreatePost,
          ),
        );
      },
    );
  }

  Widget _buildWideScreenLayout() {
    return Row(
      children: [
        // Permanent sidebar for wide screens
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(right: BorderSide(color: Colors.grey.shade200)),
          ),
          child: ProfileDrawer(),
        ),
        // Main content
        Expanded(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 900),
            child: _buildMainContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return _buildMainContent();
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                currentCategoryIndex: currentCategoryIndex,
                categories: categories,
                onCategorySelected: _onCategorySelected,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}

// Home App Bar Widget
class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  const HomeAppBar({super.key, required this.scaffoldKey});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Colors.black),
        onPressed: () {
          scaffoldKey.currentState?.openDrawer();
        },
      ),
      title: Center(
        child: Container(
          width: 40,
          height: 40,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xFF5159FF),
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/money_mouth.png',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFF5159FF),
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.white,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ),
      ),
      actions: const [
        // Empty actions - no wallet balance shown
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Home Tab Bar Widget
class HomeTabBar extends StatelessWidget {
  final TabController tabController;

  const HomeTabBar({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      // padding: const EdgeInsets.all(0),
      child: TabBar(
        controller: tabController,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey,
        indicatorColor: const Color(0xFF4C5DFF),
        indicatorWeight: 3,
        tabs: const [Tab(text: 'Explore'), Tab(text: 'Following')],
      ),
    );
  }
}

// Explore Tab Widget
class ExploreTab extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final Function(int) onCategorySelected;

  const ExploreTab({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final PostService postService = PostService();
    final currentCategory = categories[currentCategoryIndex].name;
    final topPost = postService.getTopPaidPostForCategory(currentCategory);

    return Column(
      children: [
        HorizontalCategoriesSection(
          currentCategoryIndex: currentCategoryIndex,
          categories: categories,
          onCategorySelected: onCategorySelected,
        ),
        // Top Paid Post Container (24-hour system)
        if (topPost != null)
          TopPaidPostContainer(
            category: currentCategory,
            topPost: topPost,
            onTap: () {
              // Handle tap on top paid post
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Viewing top paid post in $currentCategory'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
        Expanded(child: PostsFeed(category: currentCategory)),
      ],
    );
  }
}

// Horizontal Scrollable Categories Section Widget
class HorizontalCategoriesSection extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final Function(int) onCategorySelected;

  const HorizontalCategoriesSection({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(top: 4, bottom: 4),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = index == currentCategoryIndex;

          return GestureDetector(
            onTap: () {
              HapticFeedback.selectionClick();
              onCategorySelected(index);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOutCubic,
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              constraints: const BoxConstraints(minHeight: 42, maxHeight: 42),
              decoration: BoxDecoration(
                color: isSelected ? category.color : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected ? category.color : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      category.name,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                    // Price display removed - will be implemented with dynamic pricing later
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Category Badge Widget
class CategoryBadge extends StatelessWidget {
  final CategoryData category;

  const CategoryBadge({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: category.color,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        category.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

// Posts Feed Widget
class PostsFeed extends StatefulWidget {
  final String category;

  const PostsFeed({super.key, required this.category});

  @override
  State<PostsFeed> createState() => _PostsFeedState();
}

class _PostsFeedState extends State<PostsFeed> {
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<Post>>(
      stream: _postService.getPostsStreamSortedBy(
        'Highest Paid',
        category: widget.category,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Column(
            children: [
              const CategorySkeleton(),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
                  itemCount: 5,
                  itemBuilder: (context, index) => const PostCardSkeleton(),
                ),
              ),
            ],
          );
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final posts = snapshot.data ?? [];

        if (posts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.post_add, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No posts in ${widget.category}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Be the first to share something!',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/create_post');
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Create First Post'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF5159FF),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            HapticFeedback.mediumImpact();
            // Force refresh by reinitializing the stream
            setState(() {});
          },
          color: const Color(0xFF4285F4),
          backgroundColor: Colors.white,
          strokeWidth: 3,
          displacement: 60,
          child: ListView.builder(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ),
            padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
            itemCount: posts.length,
            itemBuilder: (context, index) {
              return PostCard(
                post: posts[index],
                onLike: () => _handleLike(posts[index]),
                onPurchase: () => _handlePurchase(posts[index]),
                onView: () => _handleView(posts[index]),
                onTap: () => _handlePostTap(posts[index]),
              );
            },
          ),
        );
      },
    );
  }

  void _handleLike(Post post) {
    _postService.likePost(post.id);
  }

  void _handlePurchase(Post post) {
    // Handle post purchase
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Purchase functionality for ${post.formattedPrice}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleView(Post post) {
    _postService.viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail view
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}

// Following Tab Widget
class FollowingTab extends StatefulWidget {
  const FollowingTab({super.key});

  @override
  State<FollowingTab> createState() => _FollowingTabState();
}

class _FollowingTabState extends State<FollowingTab> {
  final FollowService _followService = FollowService();
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();

  List<Post> _followingPosts = [];
  List<Post> _allFollowingPosts = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMorePosts = true;

  static const int _postsPerPage = 10;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadFollowingPosts();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMorePosts) {
        _loadMorePosts();
      }
    }
  }

  Future<void> _loadFollowingPosts() async {
    setState(() {
      _isLoading = true;
      _currentPage = 0;
      _followingPosts.clear();
      _allFollowingPosts.clear();
      _hasMorePosts = true;
    });

    try {
      // Get list of users current user is following
      final followingUserIds = await _followService.getFollowingStream().first;

      if (followingUserIds.isEmpty) {
        setState(() {
          _followingPosts = [];
          _allFollowingPosts = [];
          _isLoading = false;
        });
        return;
      }

      // Get all posts and filter by following users
      final allPosts = _postService.getAllPosts();
      final followingPosts =
          allPosts
              .where((post) => followingUserIds.contains(post.authorId))
              .toList();

      // Sort by creation date (newest first)
      followingPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _allFollowingPosts = followingPosts;
        _followingPosts = _getPaginatedPosts();
        _hasMorePosts = _followingPosts.length < _allFollowingPosts.length;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading following posts: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoadingMore || !_hasMorePosts) return;

    setState(() => _isLoadingMore = true);

    // Simulate network delay for smooth UX
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _currentPage++;
      final newPosts = _getPaginatedPosts();
      _followingPosts = newPosts;
      _hasMorePosts = _followingPosts.length < _allFollowingPosts.length;
      _isLoadingMore = false;
    });
  }

  List<Post> _getPaginatedPosts() {
    final endIndex = (_currentPage + 1) * _postsPerPage;
    return _allFollowingPosts
        .take(endIndex.clamp(0, _allFollowingPosts.length))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _followingPosts.isEmpty) {
      return ListView.builder(
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: 5,
        itemBuilder: (context, index) => const PostCardSkeleton(),
      );
    }

    if (_followingPosts.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No posts from followed users',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Follow other users to see their posts here',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/connect');
              },
              icon: const Icon(Icons.person_add),
              label: const Text('Find People to Follow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        await _loadFollowingPosts();
      },
      color: const Color(0xFF4285F4),
      backgroundColor: Colors.white,
      strokeWidth: 3,
      displacement: 60,
      child: ListView.builder(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: _followingPosts.length + (_hasMorePosts ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index == _followingPosts.length) {
            return _buildLoadingIndicator();
          }

          return PostCard(
            post: _followingPosts[index],
            onLike: () => _handleLike(_followingPosts[index]),
            onPurchase: () => _handlePurchase(_followingPosts[index]),
            onView: () => _handleView(_followingPosts[index]),
            onTap: () => _handlePostTap(_followingPosts[index]),
          );
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child:
            _isLoadingMore
                ? Column(
                  key: const ValueKey('loading'),
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 1200),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Transform.rotate(
                          angle: value * 2 * 3.14159,
                          child: const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Color(0xFF4285F4),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    AnimatedOpacity(
                      duration: const Duration(milliseconds: 600),
                      opacity: 0.7,
                      child: const Text(
                        'Loading more posts...',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                )
                : const SizedBox.shrink(key: ValueKey('empty')),
      ),
    );
  }

  void _handleLike(Post post) {
    _postService.likePost(post.id);
  }

  void _handlePurchase(Post post) {
    // Handle purchase functionality
  }

  void _handleView(Post post) {
    _postService.viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail screen
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}

// Home Bottom Navigation Bar Widget
class HomeBottomNavigationBar extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final VoidCallback onNavigateToCreatePost;

  const HomeBottomNavigationBar({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onNavigateToCreatePost,
  });

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF4C5DFF),
      unselectedItemColor: Colors.grey,
      currentIndex: 0,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: ''),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          label: '',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.add_box_outlined), label: ''),
        BottomNavigationBarItem(icon: Icon(Icons.search), label: ''),
        BottomNavigationBarItem(icon: Icon(Icons.person_outline), label: ''),
      ],
      onTap: (index) => _handleBottomNavTap(context, index),
    );
  }

  void _handleBottomNavTap(BuildContext context, int index) {
    switch (index) {
      case 1:
        Navigator.pushNamed(context, '/chats');
        break;
      case 2:
        onNavigateToCreatePost();
        break;
      case 3:
        Navigator.pushNamed(context, '/search');
        break;
      case 4:
        final uid = FirebaseAuth.instance.currentUser?.uid;
        if (uid != null) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => ProfileScreen(userId: uid)),
          );
        }
        break;
    }
  }
}
